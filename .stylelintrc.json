{"extends": ["stylelint-config-standard-scss", "stylelint-config-standard-vue"], "rules": {"selector-class-pattern": "^(odi-business-[a-z][a-z0-9]*(-[a-z0-9]+)*|[a-z][a-z0-9]*(-[a-z0-9]+)*)$", "scss/at-import-partial-extension": null, "scss/dollar-variable-pattern": "^[a-z][a-z0-9]*(-[a-z0-9]+)*$", "property-no-vendor-prefix": null, "value-no-vendor-prefix": null, "selector-no-vendor-prefix": null, "media-feature-name-no-vendor-prefix": null, "at-rule-no-vendor-prefix": null, "color-function-notation": "legacy", "alpha-value-notation": "number"}, "ignoreFiles": ["dist/**/*", "node_modules/**/*", "coverage/**/*", "storybook-static/**/*"]}