import { mount, VueWrapper } from '@vue/test-utils'
import { Component } from 'vue'

/**
 * 创建组件测试包装器
 */
export function createWrapper<T extends Component>(
  component: T,
  options: any = {}
): VueWrapper<any> {
  return mount(component as any, {
    global: {
      stubs: {
        transition: false,
        'transition-group': false
      }
    },
    ...options
  })
}

/**
 * 等待下一个tick
 */
export async function nextTick(): Promise<void> {
  return new Promise(resolve => {
    setTimeout(resolve, 0)
  })
}

/**
 * 触发事件并等待更新
 */
export async function triggerEvent(
  wrapper: VueWrapper<any>,
  selector: string,
  event: string,
  eventData?: any
): Promise<void> {
  await wrapper.find(selector).trigger(event, eventData)
  await wrapper.vm.$nextTick()
}

/**
 * 检查元素是否包含指定类名
 */
export function hasClass(wrapper: VueWrapper<any>, selector: string, className: string): boolean {
  const element = wrapper.find(selector)
  return element.exists() && element.classes().includes(className)
}

/**
 * 获取元素的样式属性
 */
export function getStyle(wrapper: VueWrapper<any>, selector: string, property: string): string {
  const element = wrapper.find(selector)
  if (!element.exists()) return ''

  const domElement = element.element as HTMLElement
  return getComputedStyle(domElement).getPropertyValue(property)
}

/**
 * 模拟用户输入
 */
export async function setInputValue(
  wrapper: VueWrapper<any>,
  selector: string,
  value: string
): Promise<void> {
  const input = wrapper.find(selector)
  await input.setValue(value)
  await input.trigger('input')
  await wrapper.vm.$nextTick()
}
