<template>
  <div class="app">
    <header class="app-header">
      <h1>@odi/web-odi-business</h1>
    </header>

    <main class="app-main">
      <section class="demo-section">
        <h2>组件演示</h2>
        <p>这里将展示各种业务组件的使用示例</p>

        <!-- 组件示例 -->
        <div class="component-demo">
          <h3>Button 按钮组件</h3>
          <div class="demo-group">
            <h4>基础用法</h4>
            <div class="demo-buttons">
              <Button type="primary">Primary</Button>
              <Button type="secondary">Secondary</Button>
              <Button type="success">Success</Button>
              <Button type="warning">Warning</Button>
              <Button type="danger">Danger</Button>
              <Button type="default">Default</Button>
            </div>
          </div>

          <div class="demo-group">
            <h4>不同尺寸</h4>
            <div class="demo-buttons">
              <Button size="small">Small</Button>
              <Button size="medium">Medium</Button>
              <Button size="large">Large</Button>
            </div>
          </div>

          <div class="demo-group">
            <h4>状态</h4>
            <div class="demo-buttons">
              <Button>Normal</Button>
              <Button disabled>Disabled</Button>
              <Button loading>Loading</Button>
            </div>
          </div>
        </div>
      </section>
    </main>

    <footer class="app-footer">
      <p>&copy; 2024 ODI Team. All rights reserved.</p>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { Button } from './components'
</script>

<style scoped>
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.app-header {
  background: linear-gradient(135deg, #0058a3, #0078d4);
  color: white;
  padding: 2rem;
  text-align: center;
}

.app-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.app-header p {
  margin: 0;
  font-size: 1.2rem;
  opacity: 0.9;
}

.app-main {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.demo-section h2 {
  color: #0058a3;
  margin-bottom: 1rem;
}

.component-demo {
  background: #f9f9f9;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1rem 0;
}

.component-demo h3 {
  margin-top: 0;
  color: #424242;
}

.app-footer {
  background: #f5f5f5;
  padding: 1rem;
  text-align: center;
  color: #757575;
  border-top: 1px solid #e0e0e0;
}

.app-footer p {
  margin: 0;
}

.demo-group {
  margin: 1.5rem 0;
}

.demo-group h4 {
  margin: 0 0 1rem 0;
  color: #616161;
  font-size: 1rem;
  font-weight: 500;
}

.demo-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}
</style>
