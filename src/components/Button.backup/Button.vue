<template>
  <button
    :class="buttonClasses"
    :disabled="disabled || loading"
    :type="htmlType"
    @click="handleClick"
  >
    <span v-if="loading" class="odi-business-button__loading">
      <svg class="odi-business-button__loading-icon" viewBox="0 0 24 24">
        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none" />
        <path
          d="M12 2 A10 10 0 0 1 22 12"
          stroke="currentColor"
          stroke-width="2"
          fill="none"
          stroke-linecap="round"
        />
      </svg>
    </span>
    <span class="odi-business-button__content">
      <slot />
    </span>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ButtonProps } from '@/types'

defineOptions({
  name: 'OdiButton'
})

const props = withDefaults(defineProps<ButtonProps>(), {
  variant: 'primary',
  size: 'medium',
  shape: 'default',
  htmlType: 'button',
  disabled: false,
  loading: false,
  block: false
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const buttonClasses = computed(() => [
  'odi-business-button',
  `odi-business-button--${props.variant}`,
  `odi-business-button--${props.size}`,
  `odi-business-button--${props.shape}`,
  {
    'odi-business-button--disabled': props.disabled,
    'odi-business-button--loading': props.loading,
    'odi-business-button--block': props.block
  },
  props.className
])

const handleClick = (event: MouseEvent) => {
  if (props.disabled || props.loading) {
    return
  }
  emit('click', event)
  props.onClick?.(event)
}
</script>

<style lang="scss" scoped>
@use './style';
</style>
