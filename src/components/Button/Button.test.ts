import { describe, it, expect, vi } from 'vitest'
import { createWrapper } from '@/test/utils'
import Button from './Button.vue'

describe('Button', () => {
  it('renders correctly', () => {
    const wrapper = createWrapper(Button, {
      slots: {
        default: 'Click me'
      }
    })

    expect(wrapper.text()).toBe('Click me')
    expect(wrapper.classes()).toContain('odi-business-button')
  })

  it('applies correct variant classes', () => {
    const variants = ['primary', 'secondary', 'success', 'warning', 'error', 'info'] as const

    variants.forEach(variant => {
      const wrapper = createWrapper(Button, {
        props: { variant },
        slots: { default: 'Button' }
      })

      expect(wrapper.classes()).toContain(`odi-business-button--${variant}`)
    })
  })

  it('applies correct size classes', () => {
    const sizes = ['small', 'medium', 'large'] as const

    sizes.forEach(size => {
      const wrapper = createWrapper(<PERSON><PERSON>, {
        props: { size },
        slots: { default: 'Button' }
      })

      expect(wrapper.classes()).toContain(`odi-business-button--${size}`)
    })
  })

  it('applies correct shape classes', () => {
    const shapes = ['default', 'round', 'circle'] as const

    shapes.forEach(shape => {
      const wrapper = createWrapper(Button, {
        props: { shape },
        slots: { default: 'Button' }
      })

      expect(wrapper.classes()).toContain(`odi-business-button--${shape}`)
    })
  })

  it('handles disabled state', () => {
    const wrapper = createWrapper(Button, {
      props: { disabled: true },
      slots: { default: 'Button' }
    })

    expect(wrapper.classes()).toContain('odi-business-button--disabled')
    expect(wrapper.attributes('disabled')).toBeDefined()
  })

  it('handles loading state', () => {
    const wrapper = createWrapper(Button, {
      props: { loading: true },
      slots: { default: 'Button' }
    })

    expect(wrapper.classes()).toContain('odi-business-button--loading')
    expect(wrapper.attributes('disabled')).toBeDefined()
    expect(wrapper.find('.odi-business-button__loading').exists()).toBe(true)
  })

  it('handles block prop', () => {
    const wrapper = createWrapper(Button, {
      props: { block: true },
      slots: { default: 'Button' }
    })

    expect(wrapper.classes()).toContain('odi-business-button--block')
  })

  it('emits click event', async () => {
    const wrapper = createWrapper(Button, {
      slots: { default: 'Button' }
    })

    await wrapper.trigger('click')
    expect(wrapper.emitted('click')).toBeTruthy()
  })

  it('does not emit click when disabled', async () => {
    const wrapper = createWrapper(Button, {
      props: { disabled: true },
      slots: { default: 'Button' }
    })

    await wrapper.trigger('click')
    expect(wrapper.emitted('click')).toBeFalsy()
  })

  it('does not emit click when loading', async () => {
    const wrapper = createWrapper(Button, {
      props: { loading: true },
      slots: { default: 'Button' }
    })

    await wrapper.trigger('click')
    expect(wrapper.emitted('click')).toBeFalsy()
  })

  it('calls onClick prop when provided', async () => {
    const onClick = vi.fn()
    const wrapper = createWrapper(Button, {
      props: { onClick },
      slots: { default: 'Button' }
    })

    await wrapper.trigger('click')
    expect(onClick).toHaveBeenCalled()
  })

  it('applies custom className', () => {
    const wrapper = createWrapper(Button, {
      props: { className: 'custom-class' },
      slots: { default: 'Button' }
    })

    expect(wrapper.classes()).toContain('custom-class')
  })

  it('sets correct html type', () => {
    const wrapper = createWrapper(Button, {
      props: { htmlType: 'submit' },
      slots: { default: 'Button' }
    })

    expect(wrapper.attributes('type')).toBe('submit')
  })
})
