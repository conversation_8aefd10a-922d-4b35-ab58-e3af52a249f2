// Button 组件样式
@use 'sass:color';
@use '../../styles/variables' as *;

.odi-business-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: $border-width solid transparent;
  border-radius: $border-radius-base;
  font-family: $font-family-base;
  font-weight: 500;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  transition: $transition-base;
  user-select: none;
  white-space: nowrap;
  vertical-align: middle;
  outline: none;

  &:focus-visible {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }

  // 尺寸变体
  &--small {
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-sm;
    line-height: $line-height-tight;
    min-height: 32px;
  }

  &--medium {
    padding: $spacing-sm $spacing-base;
    font-size: $font-size-base;
    line-height: $line-height-base;
    min-height: 40px;
  }

  &--large {
    padding: $spacing-base $spacing-lg;
    font-size: $font-size-lg;
    line-height: $line-height-base;
    min-height: 48px;
  }

  // 颜色变体
  &--primary {
    background-color: $primary-color;
    border-color: $primary-color;
    color: $white;

    &:hover:not(:disabled) {
      background-color: color.adjust($primary-color, $lightness: -8%);
      border-color: color.adjust($primary-color, $lightness: -8%);
    }

    &:active:not(:disabled) {
      background-color: color.adjust($primary-color, $lightness: -12%);
      border-color: color.adjust($primary-color, $lightness: -12%);
    }
  }

  &--secondary {
    background-color: $secondary-color;
    border-color: $secondary-color;
    color: $black;

    &:hover:not(:disabled) {
      background-color: color.adjust($secondary-color, $lightness: -8%);
      border-color: color.adjust($secondary-color, $lightness: -8%);
    }

    &:active:not(:disabled) {
      background-color: color.adjust($secondary-color, $lightness: -12%);
      border-color: color.adjust($secondary-color, $lightness: -12%);
    }
  }

  &--success {
    background-color: $success-color;
    border-color: $success-color;
    color: $white;

    &:hover:not(:disabled) {
      background-color: color.adjust($success-color, $lightness: -8%);
      border-color: color.adjust($success-color, $lightness: -8%);
    }

    &:active:not(:disabled) {
      background-color: color.adjust($success-color, $lightness: -12%);
      border-color: color.adjust($success-color, $lightness: -12%);
    }
  }

  &--warning {
    background-color: $warning-color;
    border-color: $warning-color;
    color: $white;

    &:hover:not(:disabled) {
      background-color: color.adjust($warning-color, $lightness: -8%);
      border-color: color.adjust($warning-color, $lightness: -8%);
    }

    &:active:not(:disabled) {
      background-color: color.adjust($warning-color, $lightness: -12%);
      border-color: color.adjust($warning-color, $lightness: -12%);
    }
  }

  &--error {
    background-color: $error-color;
    border-color: $error-color;
    color: $white;

    &:hover:not(:disabled) {
      background-color: color.adjust($error-color, $lightness: -8%);
      border-color: color.adjust($error-color, $lightness: -8%);
    }

    &:active:not(:disabled) {
      background-color: color.adjust($error-color, $lightness: -12%);
      border-color: color.adjust($error-color, $lightness: -12%);
    }
  }

  &--info {
    background-color: $info-color;
    border-color: $info-color;
    color: $white;

    &:hover:not(:disabled) {
      background-color: color.adjust($info-color, $lightness: -8%);
      border-color: color.adjust($info-color, $lightness: -8%);
    }

    &:active:not(:disabled) {
      background-color: color.adjust($info-color, $lightness: -12%);
      border-color: color.adjust($info-color, $lightness: -12%);
    }
  }

  // 形状变体
  &--round {
    border-radius: $border-radius-full;
  }

  &--circle {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    padding: 0;

    &.odi-button--small {
      width: 32px;
      height: 32px;
    }

    &.odi-button--large {
      width: 48px;
      height: 48px;
    }
  }

  // 状态
  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &--loading {
    cursor: not-allowed;
  }

  &--block {
    display: flex;
    width: 100%;
  }

  // 加载状态
  &__loading {
    display: inline-flex;
    align-items: center;
    margin-right: $spacing-xs;
  }

  &__loading-icon {
    width: 1em;
    height: 1em;
    animation: odi-business-button-spin 1s linear infinite;
  }

  &__content {
    display: inline-flex;
    align-items: center;
  }
}

@keyframes odi-business-button-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
