@use './variables' as *;

// ODI Business Components 主样式文件

// 基础样式重置
*,
*::before,
*::after {
  box-sizing: border-box;
}

// 去除默认的body margin和padding
body {
  margin: 0;
  padding: 0;
}

// 组件样式前缀
.#{$prefix} {
  font-family: $font-family-base;
  font-size: $font-size-base;
  line-height: $line-height-base;
  color: $gray-900;

  // 确保组件内的元素使用正确的盒模型
  *,
  *::before,
  *::after {
    box-sizing: border-box;
  }
}

// 工具类
.#{$prefix}-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
