import type { App } from 'vue'

// 导入样式
import './styles/index.scss'

// 导入组件
import { Button } from './components'

// 导入类型
export * from './types'

// 组件列表
const components = [
  Button
]

// 安装函数
const install = (app: App) => {
  components.forEach(component => {
    app.component(component.name!, component)
  })
}

// 导出安装函数和组件
export {
  install,
  Button
}

// 默认导出
export default {
  install
}

// 版本信息
export const version = '1.0.0'
