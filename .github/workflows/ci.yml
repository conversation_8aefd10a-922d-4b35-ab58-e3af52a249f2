name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest

      - name: Setup Node.js 18
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Run type checking
        run: pnpm run type-check

      - name: Run linting
        run: |
          pnpm run lint
          pnpm run lint:style

      - name: Run unit tests
        run: pnpm run test --coverage

      - name: Build library
        run: pnpm run build:lib
