name: 'Setup Node.js and pnpm'
description: '设置 Node.js 和 pnpm 环境，包括缓存和依赖安装'

inputs:
  node-version:
    description: 'Node.js 版本'
    required: false
    default: '18'
  pnpm-version:
    description: 'pnpm 版本'
    required: false
    default: 'latest'
  install-deps:
    description: '是否安装依赖'
    required: false
    default: 'true'
  npm-password:
    description: 'NPM registry 密码'
    required: true
  npm-username:
    description: 'NPM registry 用户名'
    required: true

outputs:
  cache-hit:
    description: '缓存是否命中'
    value: ${{ steps.cache.outputs.cache-hit }}

runs:
  using: 'composite'
  steps:
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node-version }}

    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: ${{ inputs.pnpm-version }}

    - name: Get pnpm store directory
      shell: bash
      run: |
        echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

    - name: Setup pnpm cache
      uses: actions/cache@v3
      with:
        path: ${{ env.STORE_PATH }}
        key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-store-

    - name: Configure npm registry
      shell: bash
      run: |
        cat > .npmrc << EOF
        registry=https://artifactory.cloud.ingka-system.cn/artifactory/api/npm/cn-digital-hub-npm-virtual/
        //artifactory.cloud.ingka-system.cn/artifactory/api/npm/cn-digital-hub-npm-virtual/:always-auth=true
        //artifactory.cloud.ingka-system.cn/artifactory/api/npm/cn-digital-hub-npm-virtual/:_password=${{ inputs.npm-password }}
        //artifactory.cloud.ingka-system.cn/artifactory/api/npm/cn-digital-hub-npm-virtual/:username=${{ inputs.npm-username }}
        EOF

    - name: Cache node_modules
      id: cache
      uses: actions/cache@v3
      with:
        path: |
          node_modules
          .npmrc
        key: ${{ runner.os }}-node-modules-${{ hashFiles('**/pnpm-lock.yaml') }}

    - name: Install dependencies
      if: inputs.install-deps == 'true' && steps.cache.outputs.cache-hit != 'true'
      shell: bash
      run: pnpm install
