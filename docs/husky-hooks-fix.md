# Husky Git Hooks 修复文档

## 问题描述

在提交代码时遇到以下警告：

```bash
hint: The '.husky/pre-commit' hook was ignored because it's not set as executable.
hint: You can disable this warning with `git config advice.ignoredHook false`.
hint: The '.husky/commit-msg' hook was ignored because it's not set as executable.
hint: You can disable this warning with `git config advice.ignoredHook false`.
```

同时还遇到了 commitlint 配置的 ES 模块错误。

## 根本原因

1. **权限问题**：`.husky/pre-commit` 和 `.husky/commit-msg` 文件没有执行权限
2. **ES 模块问题**：`.commitlintrc.js` 在 ES 模块环境中无法被 CommonJS 加载

## 修复方案

### 1. 修复 Husky hooks 执行权限

```bash
chmod +x .husky/pre-commit .husky/commit-msg
```

### 2. 修复 commitlint 配置文件

将 `.commitlintrc.js` 重命名为 `.commitlintrc.cjs` 以解决 ES 模块兼容性问题：

```bash
mv .commitlintrc.js .commitlintrc.cjs
```

## 验证修复

修复后的文件权限：

```bash
$ ls -la .husky/
-rwxr-xr-x   1 <USER>  <GROUP>   88 Jul 22 09:31 commit-msg
-rwxr-xr-x   1 <USER>  <GROUP>   69 Jul 22 09:31 pre-commit
```

## Git Hooks 功能

修复后，以下 Git hooks 将正常工作：

### pre-commit hook
- 运行 `lint-staged` 对暂存的文件进行代码检查
- 自动修复 ESLint 和 Stylelint 问题
- 格式化代码（Prettier）

### commit-msg hook
- 验证提交信息格式是否符合 Conventional Commits 规范
- 支持的提交类型：
  - `feat`: 新功能
  - `fix`: 修复bug
  - `docs`: 文档更新
  - `style`: 代码格式调整
  - `refactor`: 代码重构
  - `perf`: 性能优化
  - `test`: 测试相关
  - `chore`: 构建过程或辅助工具的变动
  - `revert`: 回滚
  - `build`: 构建相关

## 提交信息示例

正确的提交信息格式：

```bash
git commit -m "feat: 添加新的按钮组件"
git commit -m "fix: 修复样式显示问题"
git commit -m "docs: 更新 README 文档"
```

## 注意事项

- 确保所有团队成员在克隆项目后运行 `pnpm install` 来安装 Husky hooks
- 如果遇到权限问题，可以运行 `pnpm prepare` 重新设置 hooks
- 提交信息必须符合 Conventional Commits 规范，否则会被拒绝
