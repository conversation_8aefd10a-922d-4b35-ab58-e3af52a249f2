# GitHub Actions 兼容性修复

## 问题描述

在 GitHub Enterprise Server (GHES) 环境中运行 GitHub Actions 时，遇到了以下错误：

```
Warning: Artifact upload failed with error: GHESNotSupportedError: @actions/artifact v2.0.8+, upload-artifact@v4 and download-artifact@v4 are not currently supported on GHES.
```

## 根本原因

GitHub Actions 的某些新版本（v4）在 GitHub Enterprise Server 上不被支持，需要使用兼容的旧版本。

## 修复方案

### 1. 降级 artifact 相关 actions

将以下 actions 从 v4 降级到 v3：

- `actions/upload-artifact@v4` → `actions/upload-artifact@v3`
- `actions/download-artifact@v4` → `actions/download-artifact@v3`
- `actions/cache@v4` → `actions/cache@v3`

### 2. 更新已弃用的 actions

- `actions/create-release@v1` → `softprops/action-gh-release@v1`

### 3. 修改的文件

#### `.github/workflows/ci-cd.yml`
- 第 102 行：`actions/upload-artifact@v4` → `actions/upload-artifact@v3`
- 第 142 行：`actions/download-artifact@v4` → `actions/download-artifact@v3`
- 第 290 行：`actions/create-release@v1` → `softprops/action-gh-release@v1`

#### `.github/actions/setup-node-pnpm/action.yml`
- 第 48 行：`actions/cache@v4` → `actions/cache@v3`
- 第 67 行：`actions/cache@v4` → `actions/cache@v3`

## 兼容性说明

这些修改确保了 GitHub Actions 工作流在以下环境中都能正常运行：

- ✅ GitHub.com (公有云)
- ✅ GitHub Enterprise Server (私有部署)
- ✅ GitHub Enterprise Cloud (企业云)

## 验证方法

修复后，可以通过以下方式验证：

1. 推送代码到 `dev`、`qa` 或 `master` 分支
2. 观察 GitHub Actions 工作流是否正常运行
3. 检查构建产物是否正确上传和下载
4. 确认发布流程是否正常工作

## 注意事项

- 这些版本降级是为了兼容 GHES 环境
- 功能上没有任何损失，只是使用了稍旧但稳定的版本
- 当 GHES 支持更新版本时，可以考虑升级回 v4
