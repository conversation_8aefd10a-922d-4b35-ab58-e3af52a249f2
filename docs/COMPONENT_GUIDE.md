# 组件开发指南

本指南将帮助您了解如何在 @odi/web-odi-business 中开发新组件。

## 组件架构

### 目录结构

每个组件都应该有独立的目录，包含以下文件：

```
src/components/ComponentName/
├── ComponentName.vue      # 组件实现
├── index.ts              # 导出文件
├── style.scss            # 样式文件
└── ComponentName.test.ts # 单元测试
```

### 组件模板

#### 1. 组件实现 (ComponentName.vue)

```vue
<template>
  <div :class="componentClasses" :style="componentStyle">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ComponentNameProps } from '@/types'

defineOptions({
  name: 'OdiComponentName'
})

const props = withDefaults(defineProps<ComponentNameProps>(), {
  size: 'medium',
  disabled: false
})

const emit = defineEmits<{
  change: [value: any]
  click: [event: MouseEvent]
}>()

const componentClasses = computed(() => [
  'odi-component-name',
  `odi-component-name--${props.size}`,
  {
    'odi-component-name--disabled': props.disabled
  },
  props.className
])

const componentStyle = computed(() => ({
  ...props.style
}))
</script>

<style lang="scss" scoped>
@import './style.scss';
</style>
```

#### 2. 类型定义 (types/index.ts)

```typescript
export interface ComponentNameProps extends BaseComponentProps {
  /**
   * 组件特有属性
   */
  value?: string
  /**
   * 变化事件
   */
  onChange?: (value: string) => void
}
```

#### 3. 样式文件 (style.scss)

```scss
.odi-component-name {
  // 基础样式
  font-family: $font-family-base;

  // 尺寸变体
  &--small {
    font-size: $font-size-sm;
  }

  &--medium {
    font-size: $font-size-base;
  }

  &--large {
    font-size: $font-size-lg;
  }

  // 状态
  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}
```

#### 4. 导出文件 (index.ts)

```typescript
import ComponentName from './ComponentName.vue'
import type { App } from 'vue'

ComponentName.install = (app: App) => {
  app.component(ComponentName.name!, ComponentName)
}

export { ComponentName }
export default ComponentName
```

#### 5. 单元测试 (ComponentName.test.ts)

```typescript
import { describe, it, expect } from 'vitest'
import { createWrapper } from '@/test/utils'
import ComponentName from './ComponentName.vue'

describe('ComponentName', () => {
  it('renders correctly', () => {
    const wrapper = createWrapper(ComponentName)
    expect(wrapper.classes()).toContain('odi-component-name')
  })

  it('applies size classes', () => {
    const wrapper = createWrapper(ComponentName, {
      props: { size: 'large' }
    })
    expect(wrapper.classes()).toContain('odi-component-name--large')
  })

  it('handles disabled state', () => {
    const wrapper = createWrapper(ComponentName, {
      props: { disabled: true }
    })
    expect(wrapper.classes()).toContain('odi-component-name--disabled')
  })
})
```

#### 6. 组件文档 (README.md)

在组件目录下创建 README.md 文件，包含：

```markdown
# ComponentName 组件

## 基本用法

\`\`\`vue
<template>
<ComponentName size="medium">
内容
</ComponentName>
</template>
\`\`\`

## API

### Props

| 属性     | 类型                           | 默认值   | 说明     |
| -------- | ------------------------------ | -------- | -------- |
| size     | 'small' \| 'medium' \| 'large' | 'medium' | 组件尺寸 |
| disabled | boolean                        | false    | 是否禁用 |

### Events

| 事件名 | 说明         | 回调参数     |
| ------ | ------------ | ------------ |
| change | 值改变时触发 | (value: any) |

## 示例

### 不同尺寸

\`\`\`vue
<ComponentName size="small">小尺寸</ComponentName>
<ComponentName size="medium">中等尺寸</ComponentName>
<ComponentName size="large">大尺寸</ComponentName>
\`\`\`
```

## 开发规范

### 命名规范

1. **组件名**: 使用 PascalCase，如 `Button`、`DatePicker`
2. **文件名**: 与组件名保持一致
3. **CSS 类名**: 使用 `.odi-business-` 前缀 + kebab-case，如 `.odi-business-button`、`.odi-business-date-picker`
4. **Props**: 使用 camelCase
5. **Events**: 使用 camelCase

### 样式规范

1. **BEM 命名**:
   - Block: `.odi-business-button`
   - Element: `.odi-business-button__icon`
   - Modifier: `.odi-business-button--primary`

2. **变量使用**: 优先使用设计系统中的变量

   ```scss
   color: $primary-color;
   font-size: $font-size-base;
   spacing: $spacing-base;
   ```

3. **响应式设计**: 考虑不同屏幕尺寸的适配

### TypeScript 规范

1. **Props 接口**: 继承 `BaseComponentProps`
2. **事件类型**: 明确定义 emit 事件的类型
3. **泛型支持**: 适当使用泛型提高复用性

### 测试规范

1. **覆盖率**: 单元测试覆盖率应 >= 80%
2. **测试内容**:
   - 基础渲染
   - Props 传递
   - 事件触发
   - 状态变化
   - 边界情况

3. **测试工具**: 使用 Vitest + Vue Test Utils

## 设计系统

### 颜色系统

- Primary: `$primary-color` (#0058a3)
- Secondary: `$secondary-color` (#ffdb00)
- Success: `$success-color` (#00a651)
- Warning: `$warning-color` (#ff9500)
- Error: `$error-color` (#cc0000)
- Info: `$info-color` (#0078d4)

### 尺寸系统

- Small: 适用于紧凑布局
- Medium: 默认尺寸
- Large: 适用于重要操作

### 间距系统

- XS: 4px
- SM: 8px
- Base: 16px
- LG: 24px
- XL: 32px

## 最佳实践

### 1. 可访问性

- 提供适当的 ARIA 属性
- 支持键盘导航
- 确保颜色对比度符合标准

### 2. 性能优化

- 使用 `computed` 计算动态样式
- 避免不必要的重渲染
- 合理使用 `v-show` 和 `v-if`

### 3. 国际化

- 支持多语言
- 考虑文本方向（LTR/RTL）

### 4. 兼容性

- 支持主流浏览器
- 渐进增强设计

## 常见问题

### Q: 如何添加新的设计令牌？

A: 在 `src/styles/variables.scss` 中添加新变量，并更新相关文档。

### Q: 如何处理复杂的组件状态？

A: 使用 `computed` 属性计算复杂状态，保持模板简洁。

### Q: 如何确保组件的可复用性？

A: 通过 Props 和 Slots 提供足够的定制能力，避免硬编码。

## 发布检查清单

- [ ] 组件功能完整
- [ ] 单元测试通过
- [ ] 组件文档完整
- [ ] 类型定义正确
- [ ] 样式符合设计规范
- [ ] 可访问性测试通过
- [ ] 代码审查完成

遵循这些指南，您就能开发出高质量、一致性的组件！
