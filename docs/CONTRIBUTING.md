# 贡献指南

感谢您对 @odi/web-odi-business 项目的关注和贡献！

## 开发环境设置

### 环境要求

- Node.js >= 18
- pnpm >= 8
- Git

### 克隆项目

```bash
git clone https://github.com/your-org/web-odi-business.git
cd web-odi-business
```

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
# 启动组件开发服务器
pnpm dev

# 启动 Storybook
pnpm storybook
```

## 开发流程

### 1. 创建分支

```bash
git checkout -b feature/component-name
# 或
git checkout -b fix/issue-description
```

### 2. 开发组件

#### 组件目录结构

```
src/components/ComponentName/
├── ComponentName.vue      # 组件实现
├── index.ts              # 导出文件
├── style.scss            # 样式文件
└── ComponentName.test.ts # 单元测试
```

#### 组件开发规范

1. **命名规范**
   - 组件名使用 PascalCase
   - 文件名与组件名保持一致
   - 样式类名使用 `.odi-` 前缀

2. **TypeScript 支持**
   - 所有组件必须使用 TypeScript
   - 定义清晰的 Props 接口
   - 导出组件相关的类型定义

3. **样式规范**
   - 使用 SCSS 编写样式
   - 遵循 BEM 命名规范
   - 使用设计系统中的变量

4. **测试要求**
   - 单元测试覆盖率 >= 80%
   - 测试主要功能和边界情况
   - 使用 Vitest + Vue Test Utils

### 3. 编写文档

#### Storybook 文档

在 `stories/` 目录下创建对应的 `.stories.ts` 文件：

```typescript
import type { Meta, StoryObj } from '@storybook/vue3'
import ComponentName from '../src/components/ComponentName/ComponentName.vue'

const meta: Meta<typeof ComponentName> = {
  title: 'Components/ComponentName',
  component: ComponentName,
  // ... 配置
}

export default meta
```

#### 组件示例

- 提供基础用法示例
- 展示不同状态和变体
- 包含交互示例

### 4. 运行测试

```bash
# 单元测试
pnpm test

# 测试覆盖率
pnpm test:coverage

# E2E 测试
pnpm test:e2e

# 代码检查
pnpm lint

# 样式检查
pnpm lint:style
```

### 5. 提交代码

#### 提交信息规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

类型说明：
- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat(button): add loading state support

Add loading prop to Button component with spinner animation.

Closes #123
```

### 6. 创建 Pull Request

1. 推送分支到远程仓库
2. 在 GitHub 上创建 Pull Request
3. 填写 PR 模板
4. 等待代码审查

## 代码审查

### 审查要点

- 代码质量和可读性
- 测试覆盖率
- 文档完整性
- 设计一致性
- 性能影响

### 审查流程

1. 自动化检查（CI/CD）
2. 代码审查（至少一位维护者）
3. 测试验证
4. 合并到主分支

## 发布流程

版本发布由维护者负责，遵循语义化版本规范：

- `MAJOR`: 不兼容的 API 修改
- `MINOR`: 向下兼容的功能性新增
- `PATCH`: 向下兼容的问题修正

## 问题反馈

### Bug 报告

使用 GitHub Issues 报告 bug，请包含：

- 问题描述
- 复现步骤
- 期望行为
- 实际行为
- 环境信息
- 相关截图或代码

### 功能请求

提交功能请求时，请说明：

- 功能描述
- 使用场景
- 预期收益
- 实现建议

## 社区准则

- 保持友善和专业
- 尊重不同观点
- 提供建设性反馈
- 遵循项目规范

## 获得帮助

- 查看文档和示例
- 搜索已有 Issues
- 在 GitHub Discussions 中提问
- 联系维护者团队

感谢您的贡献！🎉
