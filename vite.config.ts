import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import dts from 'vite-plugin-dts'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const isLib = mode === 'lib'

  return {
    plugins: [vue(), ...(isLib ? [dts({ include: ['src/**/*'] })] : [])],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src')
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/styles/variables" as *;`
        }
      }
    },
    ...(isLib
      ? {
          build: {
            lib: {
              entry: resolve(__dirname, 'src/index.ts'),
              name: 'OdiWebBusiness',
              fileName: format => `index.${format === 'es' ? 'es.js' : 'js'}`
            },
            rollupOptions: {
              external: ['vue'],
              output: {
                globals: {
                  vue: 'Vue'
                }
              }
            }
          }
        }
      : {
          server: {
            port: 3300,
            open: true
          }
        }),
    test: {
      globals: true,
      environment: 'jsdom'
    }
  }
})
