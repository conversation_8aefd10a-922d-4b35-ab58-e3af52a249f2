# @odi/web-odi-business

ODI团队出品的一套业务组件库，基于 Vue 3 + TypeScript 开发。

## 特性

- 🚀 基于 Vue 3 + TypeScript 开发
- 📦 支持按需引入
- 🎨 可定制的主题系统
- 📱 响应式设计
- 🧪 完整的测试覆盖
- 📖 详细的文档和示例
- 🔧 完善的开发工具链

## 安装

```bash
# 使用 pnpm (推荐)
pnpm add @odi/web-odi-business

# 使用 npm
npm install @odi/web-odi-business

# 使用 yarn
yarn add @odi/web-odi-business
```

## 快速开始

### 完整引入

```typescript
import { createApp } from 'vue'
import OdiBusinessComponents from '@odi/web-odi-business'
import '@odi/web-odi-business/style.css'

const app = createApp(App)
app.use(OdiBusinessComponents)
app.mount('#app')
```

### 按需引入

```typescript
import { Button } from '@odi/web-odi-business'
import '@odi/web-odi-business/style.css'

// 在组件中使用
export default {
  components: {
    Button
  }
}
```

## 组件列表

- [x] Button 按钮
- [ ] Input 输入框
- [ ] Select 选择器
- [ ] DatePicker 日期选择器
- [ ] Table 表格
- [ ] Modal 模态框
- [ ] Form 表单
- [ ] 更多组件开发中...

## 开发

### 环境要求

- Node.js >= 18
- pnpm >= 8

### 开发命令

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建组件库
pnpm build:lib

# 运行测试
pnpm test

# 运行测试并生成覆盖率报告
pnpm test:coverage



# 代码检查
pnpm lint

# 样式检查
pnpm lint:style

# 代码格式化
pnpm format

# E2E 测试
pnpm test:e2e
```

### 项目结构

```
├── .github/              # GitHub Actions 配置
├── cypress/              # E2E 测试
├── docs/                 # 文档
├── src/                  # 源代码
│   ├── components/       # 组件
│   ├── styles/           # 样式
│   ├── types/            # 类型定义
│   ├── utils/            # 工具函数
│   └── test/             # 测试工具
└── dist/                 # 构建输出
```

### 组件开发规范

1. 每个组件都应该有独立的目录
2. 组件目录包含：
   - `Component.vue` - 组件实现
   - `index.ts` - 导出文件
   - `style.scss` - 样式文件
   - `Component.test.ts` - 单元测试
3. 样式使用 `.odi-` 前缀
4. 组件需要支持 TypeScript
5. 必须编写单元测试

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

MIT License

## 相关链接

- [ikeacn UI 文档](https://res-qa.app.ikea.cn/modules/storybook/ikeacn-ui/?path=/story/ikeacn-local-activitybanner--default)
- [ODI 团队](https://github.com/odi-team)
