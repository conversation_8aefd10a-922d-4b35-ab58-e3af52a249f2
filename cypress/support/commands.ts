/// <reference types="cypress" />

// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * Custom command to select DOM element by data-cy attribute.
       * @example cy.dataCy('greeting')
       */
      dataCy(value: string): Chainable<JQuery<HTMLElement>>
      
      /**
       * Custom command to mount Vue component
       * @example cy.mount(MyComponent, { props: { msg: 'Hello' } })
       */
      mount(component: any, options?: any): Chainable<any>
    }
  }
}

Cypress.Commands.add('dataCy', (value) => {
  return cy.get(`[data-cy=${value}]`)
})

// Mount command for component testing
Cypress.Commands.add('mount', (component, options = {}) => {
  // Setup options object
  const { global = {}, ...mountingOptions } = options

  // Add any global plugins or config here
  global.plugins = global.plugins || []
  global.config = global.config || {}
  global.config.globalProperties = global.config.globalProperties || {}

  return cy.mount(component, {
    global,
    ...mountingOptions
  })
})
