describe('App E2E', () => {
  beforeEach(() => {
    cy.visit('/')
  })

  it('should display the main heading', () => {
    cy.contains('h1', 'ODI Business Components').should('be.visible')
  })

  it('should display the description', () => {
    cy.contains('ODI团队出品的一套业务组件库').should('be.visible')
  })

  it('should have the correct page title', () => {
    cy.title().should('eq', 'ODI Business Components')
  })

  it('should display the demo section', () => {
    cy.contains('h2', '组件演示').should('be.visible')
    cy.contains('这里将展示各种业务组件的使用示例').should('be.visible')
  })

  it('should display the footer', () => {
    cy.contains('© 2024 ODI Team. All rights reserved.').should('be.visible')
  })
})
