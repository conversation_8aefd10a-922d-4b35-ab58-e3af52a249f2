# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules
.pnpm
.npm

# Build outputs
dist
dist-ssr
*.local
storybook-static

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Testing
coverage
.nyc_output
cypress/videos
cypress/screenshots

# Temporary files
*.tmp
*.temp

# OS generated files
Thumbs.db
.DS_Store
